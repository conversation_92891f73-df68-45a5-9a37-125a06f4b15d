package me.miguel19877.dev;

import com.bnstra.npclib.NPCLib;
import com.bnstra.npclib.api.NPC;
import com.bnstra.npclib.api.skin.Skin;
import com.google.gson.Gson;
import me.miguel19877.dev.Tasks.Leaderboards;
import me.miguel19877.dev.Tasks.LivestreamTrack;
import me.miguel19877.dev.Tasks.Minas;
import me.miguel19877.dev.commands.*;
import me.miguel19877.dev.listeners.*;
import me.miguel19877.dev.managers.AchievementManager;
import me.miguel19877.dev.managers.*;
import me.miguel19877.dev.permissions.Permissions;
import me.miguel19877.dev.utils.*;
import me.miguel19877.dev.utils.hologram.HologramListener;
import me.miguel19877.dev.utils.hologram.HologramManager;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.FallingBlock;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitRunnable;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public final class Rankup extends JavaPlugin {

    private static Rankup instance;
    public static Inventory kitInventory;
    public static Inventory warpsInventory;
    public static Inventory topMoneyInventory;
    public static Inventory topKillsInventory;
    public static Inventory topClanKillsInventory;
    public static Inventory topBlocksMinedInventory;
    public static HashMap<String, Cuboid> minas = new HashMap<>();
    public static HashMap<String, Location> minasrespawn = new HashMap<>();
    public static HashMap<Location, Minas.BlockInfo> blocks = new HashMap<>();
    public static HashMap<String, String> caixas = new HashMap<>();
    public static VoteKeyManager vkm;
    public NPC npc;
    public NPC topKillsNPC;
    public NPC topClanKillsNPC;
    public static Cuboid minapvppvp;
    public static LanguageManager languageManager;
    public static HashMap<UUID, Long> penaltyPlayers = new HashMap<>();
    public static final long PENALTY_DURATION = 10000; // 10 seconds in milliseconds
    private final Gson gson = new Gson();
    private static final String CHANNEL = "cah:playerdata";
    public final Map<UUID, PlayerData> pendingPlayerData = new ConcurrentHashMap<>();
    public PlayerDeltaManager deltaManager;

    @Override
    public void onEnable() {
        instance = this;
        // Register plugin messaging channel
        this.getServer().getMessenger().registerOutgoingPluginChannel(this, CHANNEL);
        this.getServer().getMessenger().registerIncomingPluginChannel(this, CHANNEL, new PlayerDataListener());

        // Register achievement messaging channel
        this.getServer().getMessenger().registerOutgoingPluginChannel(this, AchievementManager.ACHIEVEMENT_CHANNEL);
        this.getServer().getMessenger().registerIncomingPluginChannel(this, AchievementManager.ACHIEVEMENT_CHANNEL, new AchievementMessageListener());

        // Register top five messaging channel
        this.getServer().getMessenger().registerIncomingPluginChannel(this, "cah:topfive", new TopListener());
        // Start batching task
        new BukkitRunnable() {
            @Override
            public void run() {
                sendBatchedDeltas();
            }
        }.runTaskTimerAsynchronously(this, 20, 20); // Every 20 ticks (1 second)
        deltaManager = new PlayerDeltaManager();
        NPCLib library = new NPCLib(this);
        RedisManager.connect("localhost", 6379);
        languageManager = new LanguageManager(this);
        vkm = new VoteKeyManager(this);
        try {
            vkm.load();
        } catch (Exception e) {
            System.out.println("Erro ao carregar as chaves de voto.");
        }

        new BukkitRunnable() {
            @Override
            public void run() {
                cuboidSetup();
            }
        }.runTaskLaterAsynchronously(this, 50);
        registerCommands();
        registerEvents();
        //3*9 Kit Inventory
        kitInventory = Bukkit.createInventory(null, 27, "§6§lKit");
        ItemStack item = new ItemStack(Material.IRON_SWORD);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§6§lKit Rank");
        meta.setLore(Arrays.asList("§7§oClique para coletar o kit rank!", "Quando fizer rankup, poderá coletar outro kit do novo rank."));
        item.setItemMeta(meta);
        kitInventory.setItem(11, item);
        ItemStack item2 = new ItemStack(Material.DIAMOND_SWORD);
        ItemMeta meta2 = item2.getItemMeta();
        meta2.setDisplayName("§6§lKit VIP");
        meta2.setLore(Collections.singletonList("§7§oClique para coletar o kit VIP!"));
        item2.setItemMeta(meta2);
        kitInventory.setItem(13, item2);
        ItemStack item3 = new ItemStack(Material.STONE_SWORD);
        ItemMeta meta3 = item3.getItemMeta();
        meta3.setDisplayName("§6§lKit PvP");
        meta3.setLore(Collections.singletonList("§7§oClique para coletar o kit PvP!"));
        item3.setItemMeta(meta3);
        kitInventory.setItem(15, item3);
        //5*9 Warps Inventory surround by gray stained glass, like top row, bottom row, and sides
        warpsInventory = Bukkit.createInventory(null, 45, "§6§lWarps");
        ItemStack glass = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 7);
        ItemMeta glassMeta = glass.getItemMeta();
        glassMeta.setDisplayName(" ");
        glass.setItemMeta(glassMeta);
        for (int i = 0; i < 9; i++) {
            warpsInventory.setItem(i, glass);  // Top row
            warpsInventory.setItem(i + 36, glass);  // Bottom row
        }
        for (int i = 9; i < 36; i += 9) {
            warpsInventory.setItem(i, glass);  // Left column
            warpsInventory.setItem(i + 8, glass);  // Right column
        }
        //Bookshelf
        ItemStack bookshelf = new ItemStack(Material.BOOKSHELF);
        ItemMeta bookshelfMeta = bookshelf.getItemMeta();
        bookshelfMeta.setDisplayName("§6§lEncantamentos");
        bookshelf.setItemMeta(bookshelfMeta);
        warpsInventory.setItem(11, bookshelf);
        //Compass
        ItemStack compass = new ItemStack(Material.COMPASS);
        ItemMeta compassMeta = compass.getItemMeta();
        compassMeta.setDisplayName("§6§lSpawn");
        compass.setItemMeta(compassMeta);
        warpsInventory.setItem(15, compass);
        //Diamond
        ItemStack diamond = new ItemStack(Material.DIAMOND);
        ItemMeta diamondMeta = diamond.getItemMeta();
        diamondMeta.setDisplayName("§6§lLoja");
        diamond.setItemMeta(diamondMeta);
        warpsInventory.setItem(29, diamond);
        //Iron Pickaxe
        ItemStack ironPickaxe = new ItemStack(Material.IRON_PICKAXE);
        ItemMeta ironPickaxeMeta = ironPickaxe.getItemMeta();
        ironPickaxeMeta.setDisplayName("§6§lMinas");
        ironPickaxe.setItemMeta(ironPickaxeMeta);
        warpsInventory.setItem(22, ironPickaxe);
        //Chest
        ItemStack chest = new ItemStack(Material.CHEST);
        ItemMeta chestMeta = chest.getItemMeta();
        chestMeta.setDisplayName("§6§lCaixa");
        chest.setItemMeta(chestMeta);
        warpsInventory.setItem(33, chest);
        ItemStack plots = new ItemStack(Material.GRASS);
        ItemMeta plotsMeta = plots.getItemMeta();
        plotsMeta.setDisplayName("§6§lPlots");
        plots.setItemMeta(plotsMeta);
        warpsInventory.setItem(31, plots);
        npc = library.createNPC(Arrays.asList("§a§lTop Money", "§7Clica para ver"));
        npc.setLocation(new Location(Bukkit.getWorld("rankupspawn"), 813.5, 6, -78.5, -135, 0));
        Skin skin = new Skin("ewogICJ0aW1lc3RhbXAiIDogMTU5NjIwODg4MzQ3OCwKICAicHJvZmlsZUlkIiA6ICI5YzQ5YjU0YjFjZjU0NjZjYjRhNzA4M2JmZGQ4MDIxNSIsCiAgInByb2ZpbGVOYW1lIiA6ICJQYXVsc2libGUiLAogICJzaWduYXR1cmVSZXF1aXJlZCIgOiB0cnVlLAogICJ0ZXh0dXJlcyIgOiB7CiAgICAiU0tJTiIgOiB7CiAgICAgICJ1cmwiIDogImh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvMmZkODNhY2NhOWFmM2JiYWQ3MDVmNzE0MzU1ZDk0MTA3NDEyY2E0ZWJiZDRjZTkzOTE2MGMxYmUxMGNjZDFhMiIsCiAgICAgICJtZXRhZGF0YSIgOiB7CiAgICAgICAgIm1vZGVsIiA6ICJzbGltIgogICAgICB9CiAgICB9CiAgfQp9", "hJmESCbeBv45nrE7Pslol8e4gbi+V1x8ZUxPecaGzQ/8mtxcyEXhiGRXpxmdjZ72yZmPZT4uK4bzSm2yZppLg86m4p9sZgyg7f8FDzNsL58W2Yb2tlC7KzBLL3q4/PW+25mppqAhgkbnKSegmiSzlllF2LQDP1OkjJZGYLCOuvatdKU2xPwxIGP8jiNqFubVtIUKim2Ly/6UoSBoNlugwDMcqf+DjVmEXZxWkIQNm1btf1qp1lIGpnsLH86djWRRK07g+zgtgPjw/j9VzvhBfEj2DESyx+TkVgqp2XS4WmoZbs98Vc7d4VvRhW8ecnw1AIshAIqB1ZqbajM565lhA6j1uNU537395frd2p5z8FdGx0HdxXeRCXsdDmgeSBNmeQ2MUdtgKU+roWqLIxL3RMJa1AQBPjPGFJR26wrNtSNNCZaUyFmcoeOn43+zGy/6tkykZ+HoAA4vKm+fKC38Yitp98TVsGurnf+pycd7MpQKBSOUbYboz5RgQo5YdqQFMXy0k5gyvdOVj1c/0zkWHFGynn/t9qdM1JmfpeTsIO/7I+Aaa7gBFSAAmvvAHy79z3QLyeu2wUikDGC4R2gNk4Qy6dEPONK5apF6JYt14MrzgWYttwTFiN+vWRVDEFAaMHxDHsT+IWs5jIKw3190qK8kdALrw4csRwlyyVWLfHk=");
        npc.setSkin(skin);
        npc.create();
        topKillsNPC = library.createNPC(Arrays.asList("§c§lTop Kills", "§7Clica para ver"));
        topKillsNPC.setLocation(new Location(Bukkit.getWorld("rankupspawn"), 813.5, 6, -88.5, -45, 0));
        Skin skin1 = new Skin("ewogICJ0aW1lc3RhbXAiIDogMTU4ODYzNTMzMjA1MCwKICAicHJvZmlsZUlkIiA6ICIzM2ViZDMyYmIzMzk0YWQ5YWM2NzBjOTZjNTQ5YmE3ZSIsCiAgInByb2ZpbGVOYW1lIiA6ICJEYW5ub0JhbmFubm9YRCIsCiAgInNpZ25hdHVyZVJlcXVpcmVkIiA6IHRydWUsCiAgInRleHR1cmVzIiA6IHsKICAgICJTS0lOIiA6IHsKICAgICAgInVybCIgOiAiaHR0cDovL3RleHR1cmVzLm1pbmVjcmFmdC5uZXQvdGV4dHVyZS9hZjA1YzljYjdlNGM0NThjYTIwNGM3NmFmOTIwYWQ2NWNjYTdkOTgzZWE2NjRiNjQ2YmY4NjYxNDFmZjhlYjU0IgogICAgfQogIH0KfQ==", "LH2maaZtjnvbGa69YtWMJI3sxKIyiKLbJhGvTpQqrz6gamf0t9RpSL1O8kz7N2fwBxOI8fZNxH3tnGwAvci2ehCeC6J23DP8VjRZsaReZ2U6n8qgwLXUerUQx1//oR6oAGtoJzG+yaIjuuZvgaz/9a4SJnRlCOMKiZhqlECw1hUT5FX4dKcmajyEJec+w0eXPhwyC8x0l+S4+Ca7zKk/gO8Fe/t54am9tEWgA2pNKTkERgFJa3suSeSoNu1Oaqf7+vvrMPITdR6VSebnuoSHZhNvPTiN7jO2ugHdp6JEOLbzRCR2ROlqBJi3zSPirhGhSm+NdAATPMEMPxomiBv0oKXzvnq9qVQ+CEKjVlODv08faidW48N/VMq2LL829Apm+Q4TM9r19rL0GQoo7m8T476S2xB+vLFL28pMwo/KjlhIcAfGAYrQt3TITRJiOdiqJO0pExutqafVfOobYUXvju25EpKncvy8yK8GcgBM1E+NXhTBuwJ9tBkZg3+aazsCCQLc0nujXNOJLznv+Obzr5UqTvHmF/BGZccPLjuZQ8oHu3MtSTkOZy87H6Alv0p5D5Rf7o48zKqAnhQS43ngxmn07keY25XOW783/VwmnjclkDAV11PnJ8kigkXaleFtUb9cFv6nLcaCHM4vjk4moElKr3wpB0Zx/2IkIA7Uz0o=");
        topKillsNPC.setSkin(skin1);
        topKillsNPC.create();
        topClanKillsNPC = library.createNPC(Arrays.asList("§c§lTop Clan Kills", "§7Clica para ver"));
        topClanKillsNPC.setLocation(new Location(Bukkit.getWorld("rankupspawn"), 823.5, 6, -88.5, 45, 0));
        Skin skin2 = new Skin("ewogICJ0aW1lc3RhbXAiIDogMTYwMTQxNjE0OTQ5NSwKICAicHJvZmlsZUlkIiA6ICJmZDYwZjM2ZjU4NjE0ZjEyYjNjZDQ3YzJkODU1Mjk5YSIsCiAgInByb2ZpbGVOYW1lIiA6ICJSZWFkIiwKICAic2lnbmF0dXJlUmVxdWlyZWQiIDogdHJ1ZSwKICAidGV4dHVyZXMiIDogewogICAgIlNLSU4iIDogewogICAgICAidXJsIiA6ICJodHRwOi8vdGV4dHVyZXMubWluZWNyYWZ0Lm5ldC90ZXh0dXJlL2ZjNWI1MTQ0N2YzYzJjMGViZjk1MjZhMzE2NmEyZTMwMzFkMDc2NmUxYmYxYzMzMzA3MGUxOWM3ZDEyYWQ0YjciCiAgICB9CiAgfQp9", "YapLi78QkF/1PtN1zsSe0C5zvAArjgLe1ZPvyR0hJcKe5AoFNToYldHBnrRPf4llCP7PO/6S3OVsxjIlNe/j55pZvuZzDV8V4c7oFo/RJ/38RXnwVkTmtW6Ya/z6F7f9WcRmKdsD9r4RpCTiaAMzzMEW2uk9I5tZ0/YI8p0vzevu3iHhpPw8xjwoQEzQlu5ECIzItWIIUXQs2pP1O7VsXVrFyhFjEhw6S+sdNmB5RbxVrGmtTPGu+fZrWKXo8hDuPAV54IvUPvAw6OtvDuvhUTKpqrzu8sKxND7+nGcA0pwRd5IGJ/mwGHQXG4spYfGAvSfPGZI/aFjI17oi/JV98KLIJ4WA4ebyeaz0BU7eHeQZlKOBH5i0KEwKLoGMKdoBx39jNQDWlYtm++cA6H7G7qCHY3z2RrZ5M+7Inz9CSpDZGSD71FtZ9l5MK43a1asyJGTG6VA927PwZldun9/36c8ceDifFWxphFbNnp/FxncTFAMXQTEJ9ydxdHG+R2omrQHiLE3yBy6EAHv6L/HjoBoAuu6sGJiPgBJkCfXyHo7qwb45NHif1L1H5ZG3BEOX61Q88riHZwrFHlqo3TtMmbNbaadDsrkWXOy76/2QsEYxxznfZPc+rLwyPfNHRX8l+JXEUGdAVfzdP+CpqUnJ8wUVTK2jyBSv8d0CgGArXAM=");
        topClanKillsNPC.setSkin(skin2);
        topClanKillsNPC.create();
        Skin skin3 = new Skin("ewogICJ0aW1lc3RhbXAiIDogMTc1MDU3NDM1Mzc2NywKICAicHJvZmlsZUlkIiA6ICIxN2I5ZDBmOWYxYzE0OTE5ODRkY2Y5ZGM2YzczZDYzYyIsCiAgInByb2ZpbGVOYW1lIiA6ICJuYWJlNDk3NSIsCiAgInNpZ25hdHVyZVJlcXVpcmVkIiA6IHRydWUsCiAgInRleHR1cmVzIiA6IHsKICAgICJTS0lOIiA6IHsKICAgICAgInVybCIgOiAiaHR0cDovL3RleHR1cmVzLm1pbmVjcmFmdC5uZXQvdGV4dHVyZS80YjZlMDBmMDdiMTNjM2Q0NDlmNWUzZjk3ZGUzMTRmN2JmZDczNjExODc0ODIwODZiOTIxYjEyYzU1ZmM1MzYzIgogICAgfQogIH0KfQ==", "W2kymT3g3D0ADiw9rmVFMh66FiH3RwQykJU2Z3EdjvaGIizzrX1OoehSDYJCPx+AUNgt3kWyHZgzrXnHP/gy0wsm5+JYHtsMfp7k5SAOqO0VPWnHT3ygh6bQUuA5yLVPFeF7eMqsNpgBiC3p5O4hA4vqCQvx6UvtU6kyjuGtUJjCGvk3er9wzMSRH/pcIxHh8L6jKv/bngirCK4HjTm29JXm49lghYJeH21JqJ2fsYqvvb7yH5IuM5aB7Q2mnJ2XsxVkZ1+YhqGxbwGpjNd8ewrjIDv2E9u0ornVJgWTR/XdnUAUk7y+K/U7Xo5XKXrvqmyY6uEauL4S0LDNuqB/3hIYTIz68nvQYhFkGKAxAqpMG0ls3ZDpbCVfSEu0dEYxmYadNP0y3tCdHfQetfwfTLoHoZWT0yc1oxZNJAxu5BPQTenNzdifaMqtphFV/LQlMRU4Q0f7iHueizfZotMnclHaAO+4ZP5xKZogm8u2diZpgwGH+pGBZfXCBbk/cwJ0cZR74K9tJbwzUlsdE6sVl8wyuI96V7LFZ9I4fDVlqb4EOw5abfHATbjXIECSt/fP5GsyWE9CpDgyPUtYiQYKl9X4wspQZK4F0kxYb1YzcH8UlmNC82wk40Q7jNKVavfxJDyfBTcdPyi3eruNZVNyHOOQ4/BatZ0/4wwaNHzQJD0=");
        for (Player p : Bukkit.getOnlinePlayers()) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    loadAllPlayer(p);
                    npc.show(p);
                    topKillsNPC.show(p);
                    topClanKillsNPC.show(p);
                }
            }.runTaskAsynchronously(this);
        }
        createTopMoneyInventory();
        createTopKillsInventory();
        createTopClanKills();
        createTopBlocksMinedInventory();
        //SkyWarsStart();
        //SkyWarsLeaderBoard.start(0, 300 * 20);
        new Leaderboards().runTaskTimerAsynchronously(this, 0, 300 * 20);
        new LivestreamTrack().runTaskTimerAsynchronously(this, 0, 60 * 20);

        // Initialize achievement-related managers
        PlaytimeManager.getInstance();
        AchievementShopManager.getInstance();
        AchievementSpecialManager.getInstance();

        // Start periodic achievement checks
        AchievementRankListener.startPeriodicMoneyCheck();
        HologramManager.getInstance().initialize(this);
    }

    public static void loadAllPlayer(Player p) {
        new BukkitRunnable() {
            @Override
            public void run() {
                Permissions.loadPlayer(p);
                Economy.loadPlayer(p);
                RankSystem.loadPlayer(p);
                ClanManager.loadPlayer(p);
                BlocksMinedManager.loadPlayer(p);
                ChatListener.setChatMode(p.getUniqueId(), "global");
                KillDeathManager.loadPlayer(p);
                // TwitchManager data is now loaded via PlayerData system
                VIPManager.loadPlayer(p);
                PrestigeManager.loadPlayer(p);
            }
        }.runTaskAsynchronously(instance);
    }

    private void cuboidSetup() {
        minas.put("novato", new Cuboid(Bukkit.getWorld("minas"), 20, 37, -172, 59, 4, -222));
        minas.put("iniciante", new Cuboid(Bukkit.getWorld("minas"), 20, 4, -235, 59, 37, -285));
        minas.put("estudante", new Cuboid(Bukkit.getWorld("minas"), 20, 4, -298, 59, 37, -348));
        minas.put("aprendiz", new Cuboid(Bukkit.getWorld("minas"), 20, 4, -361, 59, 37, -411));
        minas.put("estagiario", new Cuboid(Bukkit.getWorld("minas"), 20, 4, -424, 59, 37, -474));
        minas.put("empregado", new Cuboid(Bukkit.getWorld("minas"), 20, 4, -487, 59, 37, -537));
        minas.put("promovido", new Cuboid(Bukkit.getWorld("minas"), 90, 4, -487, 129, 37, -537));
        minas.put("revisor", new Cuboid(Bukkit.getWorld("minas"), 90, 4, -424, 129, 37, -474));
        minas.put("subgerente", new Cuboid(Bukkit.getWorld("minas"), 90, 4, -361, 129, 37, -411));
        minas.put("gerente", new Cuboid(Bukkit.getWorld("minas"), 90, 4, -298, 129, 37, -348));
        minas.put("subchefe", new Cuboid(Bukkit.getWorld("minas"), 90, 4, -235, 129, 37, -285));
        minas.put("chefe", new Cuboid(Bukkit.getWorld("minas"), 90, 4, -172, 129, 37, -222));
        minas.put("presidente", new Cuboid(Bukkit.getWorld("minas"), 160, 4, -172, 199, 37, -222));
        minas.put("milionario", new Cuboid(Bukkit.getWorld("minas"), 160, 4, -235, 199, 37, -285));
        minas.put("bilionario", new Cuboid(Bukkit.getWorld("minas"), 160, 4, -298, 199, 37, -348));
        minas.put("rei", new Cuboid(Bukkit.getWorld("minas"), 160, 4, -361, 199, 37, -411));
        minas.put("divino", new Cuboid(Bukkit.getWorld("minas"), 160, 4, -424, 199, 37, -474));
        minas.put("semideus", new Cuboid(Bukkit.getWorld("minas"), 230, 4, -424, 269, 37, -474));
        minas.put("omniciente", new Cuboid(Bukkit.getWorld("minas"), 230, 4, -361, 269, 37, -411));
        minas.put("omnipotente", new Cuboid(Bukkit.getWorld("minas"), 230, 4, -298, 269, 37, -348));
        minas.put("omnipresente", new Cuboid(Bukkit.getWorld("minas"), 230, 4, -235, 269, 37, -285));
        minas.put("deus", new Cuboid(Bukkit.getWorld("minas"), 230, 4, -172, 269, 37, -222));
        minas.put("minapvp", new Cuboid(Bukkit.getWorld("minas"), 230, 4, -109, 269, 37, -159));
        minas.put("ytstreamer", new Cuboid(Bukkit.getWorld("minas"), 230, 4, -46, 269, 37, -96));
        minas.put("up", new Cuboid(Bukkit.getWorld("minas"), 230, 4, 17, 269, 37, -33));
        minas.put("vip", new Cuboid(Bukkit.getWorld("minas"), 300, 4, 17, 339, 37, -33));
        minas.put("vip+", new Cuboid(Bukkit.getWorld("minas"), 300, 4, -45, 339, 37, -95));
        minas.put("vippro", new Cuboid(Bukkit.getWorld("minas"), 300, 4, -107, 339, 37, -157));
        minas.put("vipsuper", new Cuboid(Bukkit.getWorld("minas"), 300, 4, -169, 339, 37, -219));
        minas.put("vipgod", new Cuboid(Bukkit.getWorld("minas"), 300, 4, -231, 359, 37, -281));
        minasrespawn.put("novato", new Location(Bukkit.getWorld("minas"), 15.5, 39.5, -196.5, -90, 30));
        minasrespawn.put("iniciante", new Location(Bukkit.getWorld("minas"), 15.5, 39.5, -259.5, -90, 30));
        minasrespawn.put("estudante", new Location(Bukkit.getWorld("minas"), 15.5, 39.5, -322.5, -90, 30));
        minasrespawn.put("aprendiz", new Location(Bukkit.getWorld("minas"), 15.5, 39.5, -385.5, -90, 30));
        minasrespawn.put("estagiario", new Location(Bukkit.getWorld("minas"), 15.5, 39.5, -448.5, -90, 30));
        minasrespawn.put("empregado", new Location(Bukkit.getWorld("minas"), 15.5, 39.5, -511.5, -90, 30));
        minasrespawn.put("trabalhador", new Location(Bukkit.getWorld("minas"), 85.5, 39.5, -511.5, -90, 30));
        minasrespawn.put("promovido", new Location(Bukkit.getWorld("minas"), 85.5, 39.5, -448.5, -90, 30));
        minasrespawn.put("revisor", new Location(Bukkit.getWorld("minas"), 85.5, 39.5, -385.5, -90, 30));
        minasrespawn.put("subgerente", new Location(Bukkit.getWorld("minas"), 85.5, 39.5, -322.5, -90, 30));
        minasrespawn.put("gerente", new Location(Bukkit.getWorld("minas"), 85.5, 39.5, -259.5, -90, 30));
        minasrespawn.put("subchefe", new Location(Bukkit.getWorld("minas"), 85.5, 39.5, -196.5, -90, 30));
        minasrespawn.put("chefe", new Location(Bukkit.getWorld("minas"), 155.5, 39.5, -196.5, -90, 30));
        minasrespawn.put("presidente", new Location(Bukkit.getWorld("minas"), 155.5, 39.5, -259.5, -90, 30));
        minasrespawn.put("milionario", new Location(Bukkit.getWorld("minas"), 155.5, 39.5, -322.5, -90, 30));
        minasrespawn.put("bilionario", new Location(Bukkit.getWorld("minas"), 155.5, 39.5, -385.5, -90, 30));
        minasrespawn.put("rei", new Location(Bukkit.getWorld("minas"), 155.5, 39.5, -448.5, -90, 30));
        minasrespawn.put("zeus", new Location(Bukkit.getWorld("minas"), 225.5, 39.5, -448.5, -90, 30));
        minasrespawn.put("anjo", new Location(Bukkit.getWorld("minas"), 225.5, 39.5, -385.5, -90, 30));
        minasrespawn.put("divino", new Location(Bukkit.getWorld("minas"), 225.5, 39.5, -322.5, -90, 30));
        minasrespawn.put("celestial", new Location(Bukkit.getWorld("minas"), 225.5, 39.5, -259.5, -90, 30));
        minasrespawn.put("omnipotente", new Location(Bukkit.getWorld("minas"), 225.5, 39.5, -196.5, -90, 30));
        minasrespawn.put("minapvp", new Location(Bukkit.getWorld("minas"), 225.5, 39.5, -133.5, -90, 30));
        minasrespawn.put("ytstreamer", new Location(Bukkit.getWorld("minas"), 225.5, 39.5, -70.5, -90, 30));
        minasrespawn.put("up", new Location(Bukkit.getWorld("minas"), 225.5, 39.5, 0.5, -90, 30));
        minasrespawn.put("vip", new Location(Bukkit.getWorld("minas"), 295.5, 39.5, 0.5, -90, 30));
        minasrespawn.put("vip+", new Location(Bukkit.getWorld("minas"), 295.5, 39.5, -62.5, -90, 30));
        minasrespawn.put("vippro", new Location(Bukkit.getWorld("minas"), 295.5, 39.5, -124.5, -90, 30));
        minasrespawn.put("vipsuper", new Location(Bukkit.getWorld("minas"), 295.5, 39.5, -186.5, -90, 30));
        minasrespawn.put("vipgod", new Location(Bukkit.getWorld("minas"), 295.5, 39.5, -248.5, -90, 30));
        minapvppvp = new Cuboid(Bukkit.getWorld("minas"), 228, 4, -160, 270, 4, -108);
        Minas.start(0, 20, minasrespawn, minas);
    }

    @Override
    public void onDisable() {
        //kill all fallingblocks
        this.getServer().getMessenger().unregisterOutgoingPluginChannel(this, CHANNEL);
        npc.destroy();
        if (vkm != null) {
            vkm.stopListener();
        }
        for (FallingBlock fb : Bukkit.getWorld("mundo").getEntitiesByClass(FallingBlock.class)) {
            fb.remove();
        }
    }

    public static Rankup getInstance() {
        return instance;
    }

    public void createTopMoneyInventory() {
        // 5x9 Inventory, Then basically be a pyramid with default skulls named top1, top2, etc, and around the inventory, glass pane green
        topMoneyInventory = Bukkit.createInventory(null, 45, "§a§lTop Money");
        ItemStack glass = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 5);
        ItemMeta glassMeta = glass.getItemMeta();
        glassMeta.setDisplayName(" ");
        glass.setItemMeta(glassMeta);
        for (int i = 0; i < 9; i++) {
            topMoneyInventory.setItem(i, glass);  // Top row
            topMoneyInventory.setItem(i + 36, glass);  // Bottom row
        }
        for (int i = 9; i < 36; i += 9) {
            topMoneyInventory.setItem(i, glass);  // Left column
            topMoneyInventory.setItem(i + 8, glass);  // Right
        }
        ItemStack skull = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta = (SkullMeta) skull.getItemMeta();
        skullMeta.setOwner("MHF_Arrow");
        skullMeta.setDisplayName("§6§lTop 1");
        skull.setItemMeta(skullMeta);
        topMoneyInventory.setItem(13, skull);
        ItemStack skull2 = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta2 = (SkullMeta) skull2.getItemMeta();
        skullMeta2.setOwner("MHF_Arrow");
        skullMeta2.setDisplayName("§6§lTop 2");
        skull2.setItemMeta(skullMeta2);
        topMoneyInventory.setItem(21, skull2);
        ItemStack skull3 = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta3 = (SkullMeta) skull3.getItemMeta();
        skullMeta3.setOwner("MHF_Arrow");
        skullMeta3.setDisplayName("§6§lTop 3");
        skull3.setItemMeta(skullMeta3);
        topMoneyInventory.setItem(23, skull3);
        ItemStack skull4 = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta4 = (SkullMeta) skull4.getItemMeta();
        skullMeta4.setOwner("MHF_Arrow");
        skullMeta4.setDisplayName("§6§lTop 4");
        skull4.setItemMeta(skullMeta4);
        topMoneyInventory.setItem(29, skull4);
        ItemStack skull5 = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta5 = (SkullMeta) skull5.getItemMeta();
        skullMeta5.setOwner("MHF_Arrow");
        skullMeta5.setDisplayName("§6§lTop 5");
        skull5.setItemMeta(skullMeta5);
        topMoneyInventory.setItem(33, skull5);
    }

    public void createTopBlocksMinedInventory() {
        // 5x9 Inventory, Then basically be a pyramid with default skulls named top1, top2, etc, and around the inventory, glass pane green
        topBlocksMinedInventory = Bukkit.createInventory(null, 45, "§a§lTop Blocos Minados");
        ItemStack glass = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 5);
        ItemMeta glassMeta = glass.getItemMeta();
        glassMeta.setDisplayName(" ");
        glass.setItemMeta(glassMeta);
        for (int i = 0; i < 9; i++) {
            topBlocksMinedInventory.setItem(i, glass);  // Top row
            topBlocksMinedInventory.setItem(i + 36, glass);  // Bottom row
        }
        for (int i = 9; i < 36; i += 9) {
            topBlocksMinedInventory.setItem(i, glass);  // Left column
            topBlocksMinedInventory.setItem(i + 8, glass);  // Right
        }
        ItemStack skull = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta = (SkullMeta) skull.getItemMeta();
        skullMeta.setOwner("MHF_Arrow");
        skullMeta.setDisplayName("§6§lTop 1");
        skull.setItemMeta(skullMeta);
        topBlocksMinedInventory.setItem(13, skull);
        ItemStack skull2 = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta2 = (SkullMeta) skull2.getItemMeta();
        skullMeta2.setOwner("MHF_Arrow");
        skullMeta2.setDisplayName("§6§lTop 2");
        skull2.setItemMeta(skullMeta2);
        topBlocksMinedInventory.setItem(21, skull2);
        ItemStack skull3 = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta3 = (SkullMeta) skull3.getItemMeta();
        skullMeta3.setOwner("MHF_Arrow");
        skullMeta3.setDisplayName("§6§lTop 3");
        skull3.setItemMeta(skullMeta3);
        topBlocksMinedInventory.setItem(23, skull3);
        ItemStack skull4 = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta4 = (SkullMeta) skull4.getItemMeta();
        skullMeta4.setOwner("MHF_Arrow");
        skullMeta4.setDisplayName("§6§lTop 4");
        skull4.setItemMeta(skullMeta4);
        topBlocksMinedInventory.setItem(29, skull4);
        ItemStack skull5 = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta5 = (SkullMeta) skull5.getItemMeta();
        skullMeta5.setOwner("MHF_Arrow");
        skullMeta5.setDisplayName("§6§lTop 5");
        skull5.setItemMeta(skullMeta5);
        topBlocksMinedInventory.setItem(33, skull5);
    }

    public void createTopClanKills() {
        // 5x9 Inventory, Then basically be a pyramid with default skulls named top1, top2, etc, and around the inventory, glass pane green
        topClanKillsInventory = Bukkit.createInventory(null, 45, "§6§lTop Clan Kills");
        ItemStack glass = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 5);
        ItemMeta glassMeta = glass.getItemMeta();
        glassMeta.setDisplayName(" ");
        glass.setItemMeta(glassMeta);
        for (int i = 0; i < 9; i++) {
            topClanKillsInventory.setItem(i, glass);  // Top row
            topClanKillsInventory.setItem(i + 36, glass);  // Bottom row
        }
        for (int i = 9; i < 36; i += 9) {
            topClanKillsInventory.setItem(i, glass);  // Left column
            topClanKillsInventory.setItem(i + 8, glass);  // Right
        }
        ItemStack skull = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta = (SkullMeta) skull.getItemMeta();
        skullMeta.setOwner("MHF_Arrow");
        skullMeta.setDisplayName("§6§lTop 1");
        skull.setItemMeta(skullMeta);
        topClanKillsInventory.setItem(13, skull);
        ItemStack skull2 = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta2 = (SkullMeta) skull2.getItemMeta();
        skullMeta2.setOwner("MHF_Arrow");
        skullMeta2.setDisplayName("§6§lTop 2");
        skull2.setItemMeta(skullMeta2);
        topClanKillsInventory.setItem(21, skull2);
        ItemStack skull3 = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta3 = (SkullMeta) skull3.getItemMeta();
        skullMeta3.setOwner("MHF_Arrow");
        skullMeta3.setDisplayName("§6§lTop 3");
        skull3.setItemMeta(skullMeta3);
        topClanKillsInventory.setItem(23, skull3);
        ItemStack skull4 = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta4 = (SkullMeta) skull4.getItemMeta();
        skullMeta4.setOwner("MHF_Arrow");
        skullMeta4.setDisplayName("§6§lTop 4");
        skull4.setItemMeta(skullMeta4);
        topClanKillsInventory.setItem(29, skull4);
        ItemStack skull5 = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta5 = (SkullMeta) skull5.getItemMeta();
        skullMeta5.setOwner("MHF_Arrow");
        skullMeta5.setDisplayName("§6§lTop 5");
        skull5.setItemMeta(skullMeta5);
        topClanKillsInventory.setItem(33, skull5);
    }

    public void createTopKillsInventory() {
        // 5x9 Inventory, Then basically be a pyramid with default skulls named top1, top2, etc, and around the inventory, glass pane green
        topKillsInventory = Bukkit.createInventory(null, 45, "§c§lTop Kills");
        ItemStack glass = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 14);
        ItemMeta glassMeta = glass.getItemMeta();
        glassMeta.setDisplayName(" ");
        glass.setItemMeta(glassMeta);
        for (int i = 0; i < 9; i++) {
            topKillsInventory.setItem(i, glass);  // Top row
            topKillsInventory.setItem(i + 36, glass);  // Bottom row
        }
        for (int i = 9; i < 36; i += 9) {
            topKillsInventory.setItem(i, glass);  // Left column
            topKillsInventory.setItem(i + 8, glass);  // Right
        }
        ItemStack skull = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta = (SkullMeta) skull.getItemMeta();
        skullMeta.setOwner("MHF_Arrow");
        skullMeta.setDisplayName("§6§lTop 1");
        skull.setItemMeta(skullMeta);
        topKillsInventory.setItem(13, skull);
        ItemStack skull2 = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta2 = (SkullMeta) skull2.getItemMeta();
        skullMeta2.setOwner("MHF_Arrow");
        skullMeta2.setDisplayName("§6§lTop 2");
        skull2.setItemMeta(skullMeta2);
        topKillsInventory.setItem(21, skull2);
        ItemStack skull3 = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta3 = (SkullMeta) skull3.getItemMeta();
        skullMeta3.setOwner("MHF_Arrow");
        skullMeta3.setDisplayName("§6§lTop 3");
        skull3.setItemMeta(skullMeta3);
        topKillsInventory.setItem(23, skull3);
        ItemStack skull4 = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta4 = (SkullMeta) skull4.getItemMeta();
        skullMeta4.setOwner("MHF_Arrow");
        skullMeta4.setDisplayName("§6§lTop 4");
        skull4.setItemMeta(skullMeta4);
        topKillsInventory.setItem(29, skull4);
        ItemStack skull5 = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta skullMeta5 = (SkullMeta) skull5.getItemMeta();
        skullMeta5.setOwner("MHF_Arrow");
        skullMeta5.setDisplayName("§6§lTop 5");
        skull5.setItemMeta(skullMeta5);
        topKillsInventory.setItem(33, skull5);
    }

    private void registerCommands() {
        getCommand("grupo").setExecutor(new Groups());
        getCommand("kit").setExecutor(new Kit());
        getCommand("rankup").setExecutor(new me.miguel19877.dev.commands.Rankup());
        getCommand("darcaixa").setExecutor(new DarCaixa());
        Warps w = new Warps();
        getServer().getPluginManager().registerEvents(w, this);
        getCommand("spawn").setExecutor(w);
        getCommand("minas").setExecutor(w);
        getCommand("loja").setExecutor(w);
        getCommand("money").setExecutor(new Money());
        getCommand("encantamentos").setExecutor(w);
        getCommand("warp").setExecutor(w);
        getCommand("warps").setExecutor(w);
        getCommand("lobby").setExecutor(w);
        getCommand("minapvp").setExecutor(w);
        getCommand("mina").setExecutor(new Mina());
        getCommand("chat").setExecutor(new ChatListener());
        getCommand("setarkit").setExecutor(new SetarKit());
        getCommand("clan").setExecutor(new ClanCommand());
        getCommand("votar").setExecutor(new Votar());
        getCommand("rank").setExecutor(new Rank());
        getCommand("reparar").setExecutor(new Reparar());
        getCommand("twitch").setExecutor(new Twitch());
        getCommand("live").setExecutor(new Live());
        PrivateMSG msg = new PrivateMSG();
        getCommand("privatemsg").setExecutor(msg);
        getCommand("pm").setExecutor(msg);
        getCommand("msg").setExecutor(msg);
        getCommand("tell").setExecutor(msg);
        getCommand("whisper").setExecutor(msg);
        getCommand("1v1").setExecutor(new PvPEventCommand(this));
        getCommand("prestige").setExecutor(new PrestigeCommand());
        getCommand("combatlog").setExecutor(new me.miguel19877.dev.commands.CombatLogCommand());
        getCommand("tpa").setExecutor(new me.miguel19877.dev.commands.TPACommand());
        getCommand("tpaccept").setExecutor(new me.miguel19877.dev.commands.TPAcceptCommand());
        getCommand("tpdeny").setExecutor(new me.miguel19877.dev.commands.TPDenyCommand());
        getCommand("broadcast").setExecutor(new me.miguel19877.dev.commands.BroadcastCommand());
        getCommand("streamreward").setExecutor(new StreamReward());
        getCommand("achievements").setExecutor(new Achievements());
    }

    private void registerEvents() {
        getServer().getPluginManager().registerEvents(new JoinListener(), this);
        getServer().getPluginManager().registerEvents(new QuitListener(), this);
        getServer().getPluginManager().registerEvents(new InventoryClic(), this);
        getServer().getPluginManager().registerEvents(new BlockBreakListener(), this);
        getServer().getPluginManager().registerEvents(new ChatListener(), this);
        getServer().getPluginManager().registerEvents(new InteractClick(), this);
        getServer().getPluginManager().registerEvents(new PortalListener(), this);
        getServer().getPluginManager().registerEvents(new PlacasListener(), this);
        getServer().getPluginManager().registerEvents(new CustomTabScoreboard(), this);
        getServer().getPluginManager().registerEvents(new HologramListener(HologramManager.getInstance()), this);
        getServer().getPluginManager().registerEvents(new me.miguel19877.dev.listeners.CombatLogListener(), this);
        getServer().getPluginManager().registerEvents(new NetherListener(), this);
        getServer().getPluginManager().registerEvents(new TopNPCListener(), this);
        getServer().getPluginManager().registerEvents(new ShopListener(), this);
        getServer().getPluginManager().registerEvents(new AchievementTrackingListener(), this);
        getServer().getPluginManager().registerEvents(new AchievementGUIListener(), this);
    }

    public static boolean isUnderPenalty(Player player) {
        UUID playerId = player.getUniqueId();
        if (penaltyPlayers.containsKey(playerId)) {
            long penaltyEndTime = penaltyPlayers.get(playerId);
            if (System.currentTimeMillis() < penaltyEndTime) {
                return true;
            } else {
                // Penalty time has expired
                penaltyPlayers.remove(playerId);
            }
        }
        return false;
    }

    // This method sends all batched deltas to the proxy and clears the batch
    private void sendBatchedDeltas() {
        ConcurrentHashMap<UUID, PlayerDataDelta> deltaMap = deltaManager.getAndClearDeltas();
        if (deltaMap.isEmpty()) return;

        for (Map.Entry<UUID, PlayerDataDelta> entry : deltaMap.entrySet()) {
            UUID uuid = entry.getKey();
            PlayerDataDelta delta = entry.getValue();

            Player player = Bukkit.getPlayer(uuid);
            if (player != null && player.isOnline()) {
                String json = gson.toJson(delta);
                byte[] data = json.getBytes(StandardCharsets.UTF_8);

                // Send the delta to the proxy
                player.sendPluginMessage(this, CHANNEL, data);
            }
        }
        deltaMap.clear();
    }

}
