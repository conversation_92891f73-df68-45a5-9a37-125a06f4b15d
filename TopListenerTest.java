import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

public class TopListenerTest {
    public static void main(String[] args) {
        // Test JSON parsing with the expected format
        String testJson = "{\n" +
                "  \"topMoney\": [\n" +
                "    {\"position\": 1, \"username\": \"Player1\", \"value\": 1000000},\n" +
                "    {\"position\": 2, \"username\": \"Player2\", \"value\": 900000}\n" +
                "  ],\n" +
                "  \"topKills\": [\n" +
                "    {\"position\": 1, \"username\": \"Player3\", \"value\": 5000},\n" +
                "    {\"position\": 2, \"username\": \"Player4\", \"value\": 4500}\n" +
                "  ],\n" +
                "  \"topBlocksMined\": [\n" +
                "    {\"position\": 1, \"username\": \"Player3\", \"value\": 5000},\n" +
                "    {\"position\": 2, \"username\": \"Player4\", \"value\": 4500}\n" +
                "  ],\n" +
                "  \"timestamp\": 1234567890123\n" +
                "}";

        Gson gson = new Gson();
        Type mapType = new TypeToken<Map<String, Object>>(){}.getType();
        Map<String, Object> data = gson.fromJson(testJson, mapType);

        // Parse the top lists
        List<Map<String, Object>> topMoney = (List<Map<String, Object>>) data.get("topMoney");
        List<Map<String, Object>> topKills = (List<Map<String, Object>>) data.get("topKills");
        List<Map<String, Object>> topBlocksMined = (List<Map<String, Object>>) data.get("topBlocksMined");

        System.out.println("Top Money:");
        for (Map<String, Object> player : topMoney) {
            String username = (String) player.get("username");
            Number value = (Number) player.get("value");
            System.out.println("  " + username + ": " + value.longValue());
        }

        System.out.println("Top Kills:");
        for (Map<String, Object> player : topKills) {
            String username = (String) player.get("username");
            Number value = (Number) player.get("value");
            System.out.println("  " + username + ": " + value.intValue());
        }

        System.out.println("Top Blocks Mined:");
        for (Map<String, Object> player : topBlocksMined) {
            String username = (String) player.get("username");
            Number value = (Number) player.get("value");
            System.out.println("  " + username + ": " + value.longValue());
        }
    }
}
