package me.miguel19877.dev.commands;

import me.miguel19877.dev.<PERSON>up;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.utils.hologram.HologramManager;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.scheduler.BukkitRunnable;

public class Warps implements CommandExecutor, Listener {

    private static boolean yes = false;
    private static boolean yes2 = false;
    @Override
    public boolean onCommand(CommandSender sender, Command command, String s, String[] strings) {
        if (command.getName().equalsIgnoreCase("warp") || command.getName().equalsIgnoreCase("warps")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                p.openInventory(Rankup.warpsInventory);
            }else {
                sender.sendMessage("§c§lThis command can only be used by players!");
            }
            return true;
        }
        if (command.getName().equalsIgnoreCase("encantamentos")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                LanguageManager.getInstance().sendMessage(p, "general.under_construction");
            }else {
                sender.sendMessage("§c§lThis command can only be used by players!");
            }
            return true;
        }
        if (command.getName().equalsIgnoreCase("loja")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                loja(p);
            }else {
                sender.sendMessage("§c§lThis command can only be used by players!");
            }
            return true;
        }
        if (command.getName().equalsIgnoreCase("spawn") || command.getName().equalsIgnoreCase("lobby")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                spawn(p);
            }else {
                sender.sendMessage("§c§lThis command can only be used by players!");
            }
            return true;
        }
        if (command.getName().equalsIgnoreCase("minas")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                minas(p);
            }else {
                sender.sendMessage("§c§lThis command can only be used by players!");
            }
            return true;
        }
        if (command.getName().equalsIgnoreCase("minapvp")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                minapvp(p);
            }else {
                sender.sendMessage("§c§lThis command can only be used by players!");
            }
            return true;
        }
        return false;
    }

    private void encantamentos(Player p) {
        p.teleport(new Location(Bukkit.getWorld("mundo"), 633.5, 4.5, 51.5));
        LanguageManager.getInstance().sendMessage(p, "warps.teleported_enchantments");
    }

    private void minapvp(Player p) {
        p.teleport(new Location(Bukkit.getWorld("minas"), 213.5, 41.3, -133.5, -90, 0));
        if (!yes2) {
            yes2 = true;
            new BukkitRunnable() {
                @Override
                public void run() {
                    HologramManager h = HologramManager.getInstance();
                    h.createHologram("minapvp",new Location(Bukkit.getWorld("minas"), 227.0, 40, -134.0), "§fLinha Vermelha §cPVP ON (Perdes os Itens)");
                    h.createHologram("minapvp2",new Location(Bukkit.getWorld("minas"), 227.0, 40, -151.0), "§fLinha Vermelha §cPVP ON (Perdes os Itens)");
                    h.createHologram("minapvp3",new Location(Bukkit.getWorld("minas"), 227.0, 40, -117.0), "§fLinha Vermelha §cPVP ON (Perdes os Itens)");
                }
            }.runTaskLater(Rankup.getInstance(), 40L);
        }
        LanguageManager.getInstance().sendMessage(p, "warps.teleported_minapvp");
    }

    private void loja(Player p) {
        p.teleport(new Location(Bukkit.getWorld("loja"), 261.5, 30.5, 196.5, 45, 0));
        LanguageManager.getInstance().sendMessage(p, "warps.teleported_shop");
    }

    private void spawn(Player p) {
        p.teleport(new Location(Bukkit.getWorld("rankupspawn"), 818.5, 5.5, -83.5, 0, 0));
        LanguageManager.getInstance().sendMessage(p, "warps.teleported_spawn");
    }

    private void minas(Player p) {
        p.teleport(new Location(Bukkit.getWorld("rankupspawn"), 614.5, 6.5, -71.5));
        if (!yes) {
            yes = true;
            new BukkitRunnable() {
                @Override
                public void run() {
                    HologramManager h = HologramManager.getInstance();
                    h.createHologram("0",new Location(Bukkit.getWorld("rankupspawn"), 581.0, 3, -53.5), "§fNovato");
                    h.createHologram("1",new Location(Bukkit.getWorld("rankupspawn"), 574.0, 3, -52.5), "§dIniciante");
                    h.createHologram("2",new Location(Bukkit.getWorld("rankupspawn"), 570.5, 3, -59), "§bEstudante");
                    h.createHologram("3",new Location(Bukkit.getWorld("rankupspawn"), 574.0, 3, -64.5), "§1Aprendiz");
                    h.createHologram("4",new Location(Bukkit.getWorld("rankupspawn"), 581.0, 3, -64.5), "§7Estagiário");
                    h.createHologram("5",new Location(Bukkit.getWorld("rankupspawn"), 580.0, 3, -78.5), "§5Empregado");
                    h.createHologram("6",new Location(Bukkit.getWorld("rankupspawn"), 573.5, 3, -83), "§3Promovido");
                    h.createHologram("7",new Location(Bukkit.getWorld("rankupspawn"), 572.5, 4, -91), "§1Revisor");
                    h.createHologram("8",new Location(Bukkit.getWorld("rankupspawn"), 579.0, 3, -93.5), "§eSub-Gerente");
                    h.createHologram("9",new Location(Bukkit.getWorld("rankupspawn"), 584.5, 3, -89), "§cGerente");
                    h.createHologram("10",new Location(Bukkit.getWorld("rankupspawn"), 595.5, 3, -111), "§aSub-Chefe");
                    h.createHologram("11",new Location(Bukkit.getWorld("rankupspawn"), 595.5, 3, -118), "§8Chefe");
                    h.createHologram("12",new Location(Bukkit.getWorld("rankupspawn"), 601, 3, -120.5), "§6Presidente");
                    h.createHologram("13",new Location(Bukkit.getWorld("rankupspawn"), 605.5, 3, -118), "§4Milionário");
                    h.createHologram("14",new Location(Bukkit.getWorld("rankupspawn"), 605.5, 3, -111), "§0Bilio§cnário");
                    h.createHologram("15",new Location(Bukkit.getWorld("rankupspawn"), 611.5, 3, -113), "§0R§aei");
                    h.createHologram("16",new Location(Bukkit.getWorld("rankupspawn"), 611.5, 3, -120), "§0Div§8ino");
                    h.createHologram("17",new Location(Bukkit.getWorld("rankupspawn"), 617, 3, -122.5), "§0Semi§6Deus");
                    h.createHologram("18",new Location(Bukkit.getWorld("rankupspawn"), 621.5, 3, -120), "§0Omni§5ciente");
                    h.createHologram("19",new Location(Bukkit.getWorld("rankupspawn"), 621.5, 3, -113), "§0Omni§4potente");
                    h.createHologram("20",new Location(Bukkit.getWorld("rankupspawn"), 648, 4, -89.5), "§0Omni§2presente");
                    h.createHologram("21",new Location(Bukkit.getWorld("rankupspawn"), 655, 5, -87.5), "§0§k§la§0§lDEUS§0§K§la");
                    h.createHologram("22",new Location(Bukkit.getWorld("rankupspawn"), 658.5, 5, -82), "§6§lMina PvP");
                    h.createHologram("23",new Location(Bukkit.getWorld("rankupspawn"), 655, 5, -77.5), "§f[Y§4T]§f/§5Streamer");
                    h.createHologram("24",new Location(Bukkit.getWorld("rankupspawn"), 648, 4, -76.5), "§cuP");
                    h.createHologram("25",new Location(Bukkit.getWorld("rankupspawn"), 645, 4, -65.5), "§7[VIP]");
                    h.createHologram("26",new Location(Bukkit.getWorld("rankupspawn"), 652, 3, -65.5), "§7[VIP+]");
                    h.createHologram("27",new Location(Bukkit.getWorld("rankupspawn"), 654.5, 3, -61), "§7[VIP PRO]");
                    h.createHologram("28",new Location(Bukkit.getWorld("rankupspawn"), 652, 4, -56.5), "§7[VIP SUPER]");
                    h.createHologram("29",new Location(Bukkit.getWorld("rankupspawn"), 647, 5, -55.5), "§7[VIP GOD]");
                }
            }.runTaskLater(Rankup.getInstance(), 40L);
        }
        LanguageManager.getInstance().sendMessage(p, "warps.teleported_mines");
    }

    private void plots(Player p) {
        p.teleport(new Location(Bukkit.getWorld("plotworld"), 0.0, 66.5, 0.0));
        LanguageManager.getInstance().sendMessage(p, "warps.teleported_plots");
    }

    @EventHandler
    public void clickWarpsInventory(InventoryClickEvent e) {
        if (e.getInventory().equals(Rankup.warpsInventory)) {
            e.setCancelled(true);
            if(e.getCurrentItem() == null) return;
            if (e.getCurrentItem() != null && e.getCurrentItem().hasItemMeta() && e.getCurrentItem().getItemMeta().hasDisplayName()) {
                Player p = (Player) e.getWhoClicked();
                if (e.getCurrentItem().getItemMeta().getDisplayName().equals("§6§lEncantamentos")) {
                    LanguageManager.getInstance().sendMessage(p, "general.under_construction");
                }
                if (e.getCurrentItem().getItemMeta().getDisplayName().equals("§6§lCaixa")) {
                    LanguageManager.getInstance().sendMessage(p, "warps.crate_development");
                }
                if (e.getCurrentItem().getItemMeta().getDisplayName().equals("§6§lLoja")) {
                    loja(p);
                }
                if (e.getCurrentItem().getItemMeta().getDisplayName().equals("§6§lSpawn")) {
                    spawn(p);
                }
                if (e.getCurrentItem().getItemMeta().getDisplayName().equals("§6§lMinas")) {
                    minas(p);
                }
                if (e.getCurrentItem().getItemMeta().getDisplayName().equals("§6§lPlots")) {
                    plots(p);
                }
            }
            e.getWhoClicked().closeInventory();
        }
    }
}
