package me.miguel19877.dev.utils.hologram;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitTask;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * Singleton manager for centralized hologram management.
 * Handles hologram creation, retrieval, removal, and lifecycle management.
 */
public class HologramManager {

    private static HologramManager instance;
    private final Map<String, Hologram> holograms;
    private final Logger logger;
    private final HologramOptimizer optimizer;
    private BukkitTask updateTask;
    private Plugin plugin;
    private HologramListener hologramListener;

    /**
     * Private constructor for singleton pattern.
     */
    private HologramManager() {
        this.holograms = new ConcurrentHashMap<>();
        this.logger = Bukkit.getLogger();
        this.optimizer = new HologramOptimizer();
    }

    /**
     * Gets the singleton instance of HologramManager.
     *
     * @return The HologramManager instance
     */
    public static HologramManager getInstance() {
        if (instance == null) {
            instance = new HologramManager();
        }
        return instance;
    }

    /**
     * Initializes the hologram manager with the plugin instance.
     * This should be called during plugin startup.
     *
     * @param plugin The plugin instance
     */
    public void initialize(Plugin plugin) {
        this.plugin = plugin;

        // Initialize and register the hologram listener for error recovery
        this.hologramListener = new HologramListener(this);
        Bukkit.getPluginManager().registerEvents(hologramListener, plugin);

        startUpdateTask();
        logger.info("HologramManager initialized with " + plugin.getName() + " (error recovery enabled)");
    }

    /**
     * Validates parameters for hologram creation.
     *
     * @param id The hologram ID to validate
     * @param location The location to validate
     * @param text The text to validate
     * @throws IllegalArgumentException if any parameter is invalid
     */
    private void validateHologramCreationParameters(String id, Location location, String text) {
        if (id == null || id.trim().isEmpty()) {
            throw new IllegalArgumentException("Hologram ID cannot be null or empty");
        }

        if (id.length() > 64) {
            throw new IllegalArgumentException("Hologram ID cannot exceed 64 characters: '" + id + "'");
        }

        if (location == null) {
            throw new IllegalArgumentException("Location cannot be null");
        }

        if (location.getWorld() == null) {
            throw new IllegalArgumentException("Location world cannot be null");
        }

        if (!location.getWorld().isChunkLoaded(location.getBlockX() >> 4, location.getBlockZ() >> 4)) {
            throw new IllegalArgumentException("Location chunk is not loaded: " + formatLocation(location));
        }

        // Validate Y coordinate is within reasonable bounds
        if (location.getY() < -64 || location.getY() > 320) {
            throw new IllegalArgumentException("Location Y coordinate out of reasonable bounds (-64 to 320): " + formatLocation(location));
        }

        if (text == null) {
            throw new IllegalArgumentException("Text cannot be null (use empty string instead)");
        }

        // Check for reasonable text length (after color code processing)
        String processedText = text.replaceAll("&[0-9a-fk-or]", "");
        if (processedText.length() > 256) {
            throw new IllegalArgumentException("Text content too long (max 256 characters after color codes): " + processedText.length());
        }
    }

    /**
     * Creates a new hologram with the specified parameters.
     *
     * @param id Unique identifier for the hologram
     * @param location Location where the hologram should appear
     * @param text Initial text to display
     * @return The created hologram, or null if creation failed
     * @throws IllegalArgumentException if a hologram with the same ID already exists or parameters are invalid
     */
    public Hologram createHologram(String id, Location location, String text) {
        return createHologram(id, location, text, 0.0, true);
    }

    /**
     * Creates a new hologram with full customization options.
     *
     * @param id Unique identifier for the hologram
     * @param location Location where the hologram should appear
     * @param text Initial text to display
     * @param heightOffset Vertical offset from the base location
     * @param visible Whether the hologram should be visible initially
     * @return The created hologram, or null if creation failed
     * @throws IllegalArgumentException if a hologram with the same ID already exists or parameters are invalid
     */
    public Hologram createHologram(String id, Location location, String text, double heightOffset, boolean visible) {
        // Validate input parameters
        validateHologramCreationParameters(id, location, text);

        // Check for ID uniqueness
        if (holograms.containsKey(id)) {
            throw new IllegalArgumentException("Hologram with ID '" + id + "' already exists");
        }

        try {
            Hologram hologram = new Hologram(id, location, text, heightOffset, visible);
            holograms.put(id, hologram);
            logger.info("Created hologram '" + id + "' at " + formatLocation(location));
            return hologram;
        } catch (IllegalArgumentException e) {
            // Re-throw validation errors
            throw e;
        } catch (Exception e) {
            logger.severe("Failed to create hologram '" + id + "': " + e.getMessage());
            // Graceful degradation - return null instead of crashing
            return null;
        }
    }

    /**
     * Retrieves a hologram by its unique identifier.
     *
     * @param id The hologram ID
     * @return The hologram instance, or null if not found
     */
    public Hologram getHologram(String id) {
        return holograms.get(id);
    }

    /**
     * Removes a hologram by its unique identifier.
     *
     * @param id The hologram ID
     * @return true if the hologram was removed, false if it didn't exist
     */
    public boolean removeHologram(String id) {
        Hologram hologram = holograms.remove(id);
        if (hologram != null) {
            hologram.remove();
            logger.info("Removed hologram '" + id + "'");
            return true;
        }
        return false;
    }

    /**
     * Checks if a hologram with the given ID exists.
     *
     * @param id The hologram ID
     * @return true if the hologram exists, false otherwise
     */
    public boolean hasHologram(String id) {
        return holograms.containsKey(id);
    }

    /**
     * Gets the number of active holograms.
     *
     * @return The hologram count
     */
    public int getHologramCount() {
        return holograms.size();
    }

    /**
     * Starts the periodic update task for hologram maintenance.
     * This task runs every second to clean up invalid holograms.
     */
    private void startUpdateTask() {
        if (plugin != null && updateTask == null) {
            updateTask = Bukkit.getScheduler().runTaskTimer(plugin, new Runnable() {
                @Override
                public void run() {
                    performMaintenance();
                }
            }, 20L, 20L);
            logger.info("Started hologram maintenance task");
        }
    }

    /**
     * Performs periodic maintenance and optimization on all holograms.
     * Uses HologramOptimizer for batch processing and performance filtering.
     */
    private void performMaintenance() {
        // Run optimization updates with batch processing and distance filtering
        optimizer.optimizeUpdates(holograms);

        // Clean up invalid holograms using the optimizer
        int removedCount = optimizer.cleanupInvalidHolograms(holograms);

        // Clean up old recovery data (older than 5 minutes)
        if (hologramListener != null) {
            hologramListener.cleanupOldRecoveryData(5 * 60 * 1000L); // 5 minutes
        }

        // Log maintenance statistics periodically
        if (removedCount > 0 || holograms.size() > 10) {
            Map<String, Object> stats = optimizer.getOptimizationStats();
            int recoveryDataCount = hologramListener != null ? hologramListener.getRecoveryDataCount() : 0;
            logger.fine("Hologram maintenance - Active: " + holograms.size() +
                    ", Cleaned: " + removedCount +
                    ", Recovery data: " + recoveryDataCount +
                    ", Players: " + stats.get("onlinePlayerCount"));
        }
    }

    /**
     * Cleans up all holograms and stops the update task.
     * This should be called during plugin shutdown.
     */
    public void cleanup() {
        // Stop the update task
        if (updateTask != null) {
            updateTask.cancel();
            updateTask = null;
        }

        // Remove all holograms
        int count = holograms.size();
        for (Hologram hologram : holograms.values()) {
            hologram.remove();
        }
        holograms.clear();

        logger.info("HologramManager cleanup completed. Removed " + count + " holograms");
    }

    /**
     * Formats a location for logging purposes.
     *
     * @param location The location to format
     * @return Formatted location string
     */
    private String formatLocation(Location location) {
        return String.format("(%s: %.1f, %.1f, %.1f)",
                location.getWorld().getName(),
                location.getX(),
                location.getY(),
                location.getZ());
    }

    /**
     * Gets a copy of all active holograms for debugging purposes.
     *
     * @return Map of hologram IDs to hologram instances
     */
    public Map<String, Hologram> getAllHolograms() {
        return new HashMap<>(holograms);
    }

    /**
     * Adds a hologram to the registry (used by error recovery system).
     * This method bypasses normal validation since it's used for recreation.
     *
     * @param hologram The hologram to add
     * @return true if added successfully, false otherwise
     */
    protected boolean addHologramToRegistry(Hologram hologram) {
        if (hologram == null || hologram.getId() == null) {
            return false;
        }

        try {
            holograms.put(hologram.getId(), hologram);
            logger.info("Recreated hologram '" + hologram.getId() + "' at " + formatLocation(hologram.getLocation()));
            return true;
        } catch (Exception e) {
            logger.warning("Failed to add recreated hologram to registry: " + e.getMessage());
            return false;
        }
    }

    /**
     * Validates that a hologram ID is valid for operations.
     *
     * @param id The hologram ID to validate
     * @return true if the ID is valid, false otherwise
     */
    private boolean validateHologramId(String id) {
        return id != null && !id.trim().isEmpty() && id.length() <= 64;
    }

    /**
     * Validates that a hologram exists and is still valid.
     *
     * @param id The hologram ID
     * @return true if the hologram exists and is valid, false otherwise
     */
    public boolean isHologramValid(String id) {
        if (!validateHologramId(id)) {
            return false;
        }

        Hologram hologram = holograms.get(id);
        return hologram != null && hologram.isValid();
    }

    /**
     * Updates a hologram's text with change detection to avoid unnecessary updates.
     * This method integrates with the optimizer to track update patterns.
     *
     * @param id The hologram ID
     * @param newText The new text to display
     * @return true if the text was updated, false if no change was needed or hologram not found
     */
    public boolean updateHologramText(String id, String newText) {
        if (!validateHologramId(id)) {
            logger.warning("Invalid hologram ID for update: '" + id + "'");
            return false;
        }

        Hologram hologram = holograms.get(id);
        if (hologram == null) {
            logger.fine("Hologram not found for update: '" + id + "'");
            return false;
        }

        if (!hologram.isValid()) {
            logger.warning("Hologram '" + id + "' is no longer valid, removing from registry");
            holograms.remove(id);
            return false;
        }

        // Check if text actually changed to avoid unnecessary updates
        if (newText != null && newText.equals(hologram.getCurrentText())) {
            return false; // No change needed
        }

        try {
            // Update the text and mark for optimization tracking
            hologram.setText(newText);
            optimizer.markForUpdate(id);
            return true;
        } catch (Exception e) {
            logger.warning("Failed to update hologram text for '" + id + "': " + e.getMessage());
            return false;
        }
    }

    /**
     * Forces an immediate update for a specific hologram, bypassing optimization filters.
     * Use this method sparingly for critical updates that must happen immediately.
     *
     * @param id The hologram ID
     * @param newText The new text to display
     * @return true if the update was successful, false if hologram not found or invalid
     */
    public boolean forceUpdateHologram(String id, String newText) {
        if (!validateHologramId(id)) {
            logger.warning("Invalid hologram ID for force update: '" + id + "'");
            return false;
        }

        Hologram hologram = holograms.get(id);
        if (hologram == null) {
            logger.fine("Hologram not found for force update: '" + id + "'");
            return false;
        }

        if (!hologram.isValid()) {
            logger.warning("Hologram '" + id + "' is no longer valid, removing from registry");
            holograms.remove(id);
            return false;
        }

        try {
            hologram.setText(newText);
            return true;
        } catch (Exception e) {
            logger.warning("Failed to force update hologram text for '" + id + "': " + e.getMessage());
            return false;
        }
    }

    /**
     * Gets the hologram optimizer instance for advanced operations.
     *
     * @return The HologramOptimizer instance
     */
    public HologramOptimizer getOptimizer() {
        return optimizer;
    }

    /**
     * Gets optimization statistics for monitoring hologram performance.
     *
     * @return Map containing optimization statistics
     */
    public Map<String, Object> getOptimizationStats() {
        return optimizer.getOptimizationStats();
    }
}