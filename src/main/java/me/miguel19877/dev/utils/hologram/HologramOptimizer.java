package me.miguel19877.dev.utils.hologram;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.Player;

import java.util.*;
import java.util.logging.Logger;

/**
 * Performance optimizer for hologram updates with batch processing and distance-based filtering.
 * Implements optimization strategies to reduce server load when managing multiple holograms.
 */
public class HologramOptimizer {

    private static final int MAX_UPDATE_DISTANCE = 50; // blocks
    private static final int BATCH_SIZE = 10; // holograms per batch
    private static final long MIN_UPDATE_INTERVAL = 1000; // milliseconds

    private final Logger logger;
    private final Map<String, Long> lastUpdateTimes;
    private final Set<String> pendingUpdates;

    /**
     * Creates a new HologramOptimizer instance.
     */
    public HologramOptimizer() {
        this.logger = Bukkit.getLogger();
        this.lastUpdateTimes = new HashMap<>();
        this.pendingUpdates = new HashSet<>();
    }

    /**
     * Optimizes hologram updates by batching operations and filtering based on player proximity.
     * This method should be called periodically to process pending updates efficiently.
     *
     * @param holograms Map of all active holograms
     */
    public void optimizeUpdates(Map<String, Hologram> holograms) {
        if (holograms.isEmpty()) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        List<Hologram> updateCandidates = new ArrayList<>();

        // Filter holograms that need updates and are within range of players
        for (Hologram hologram : holograms.values()) {
            if (shouldUpdate(hologram, currentTime)) {
                updateCandidates.add(hologram);
            }
        }

        // Process updates in batches to prevent server lag
        processBatchedUpdates(updateCandidates, currentTime);

        // Log optimization statistics if significant activity
        if (updateCandidates.size() > 5) {
            logger.fine("HologramOptimizer processed " + updateCandidates.size() + " update candidates");
        }
    }

    /**
     * Determines if a hologram should be updated based on various optimization criteria.
     *
     * @param hologram The hologram to check
     * @param currentTime Current system time in milliseconds
     * @return true if the hologram should be updated, false otherwise
     */
    public boolean shouldUpdate(Hologram hologram, long currentTime) {
        if (hologram == null || !hologram.isValid()) {
            return false;
        }

        String hologramId = hologram.getId();

        // Check minimum update interval to prevent excessive updates
        Long lastUpdate = lastUpdateTimes.get(hologramId);
        if (lastUpdate != null && (currentTime - lastUpdate) < MIN_UPDATE_INTERVAL) {
            return false;
        }

        // Check if any players are within update distance
        if (!hasPlayersNearby(hologram.getLocation())) {
            return false;
        }

        // Check if hologram is in a loaded chunk
        if (!isChunkLoaded(hologram.getLocation())) {
            return false;
        }

        return true;
    }

    /**
     * Overloaded method that uses current system time.
     *
     * @param hologram The hologram to check
     * @return true if the hologram should be updated, false otherwise
     */
    public boolean shouldUpdate(Hologram hologram) {
        return shouldUpdate(hologram, System.currentTimeMillis());
    }

    /**
     * Processes hologram updates in batches to prevent server performance issues.
     *
     * @param updateCandidates List of holograms that need updates
     * @param currentTime Current system time in milliseconds
     */
    private void processBatchedUpdates(List<Hologram> updateCandidates, long currentTime) {
        int processed = 0;

        for (Hologram hologram : updateCandidates) {
            if (processed >= BATCH_SIZE) {
                // Defer remaining updates to next cycle
                break;
            }

            // Mark as updated (even if no actual text change occurs)
            lastUpdateTimes.put(hologram.getId(), currentTime);
            processed++;
        }

        if (processed > 0) {
            logger.fine("Processed " + processed + " hologram updates in batch");
        }
    }

    /**
     * Checks if there are any players within the update distance of a location.
     *
     * @param location The location to check around
     * @return true if players are nearby, false otherwise
     */
    private boolean hasPlayersNearby(Location location) {
        if (location.getWorld() == null) {
            return false;
        }

        Collection<? extends Player> players = Bukkit.getOnlinePlayers();
        if (players.isEmpty()) {
            return false;
        }

        double maxDistanceSquared = MAX_UPDATE_DISTANCE * MAX_UPDATE_DISTANCE;

        for (Player player : players) {
            if (player.getWorld().equals(location.getWorld())) {
                double distanceSquared = player.getLocation().distanceSquared(location);
                if (distanceSquared <= maxDistanceSquared) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Checks if the chunk at the given location is loaded.
     *
     * @param location The location to check
     * @return true if the chunk is loaded, false otherwise
     */
    private boolean isChunkLoaded(Location location) {
        if (location.getWorld() == null) {
            return false;
        }

        return location.getWorld().isChunkLoaded(location.getBlockX() >> 4, location.getBlockZ() >> 4);
    }

    /**
     * Cleans up invalid holograms from the tracking maps and removes them from the hologram registry.
     * This method helps prevent memory leaks by removing references to destroyed holograms.
     *
     * @param holograms Map of all active holograms (will be modified)
     * @return Number of invalid holograms that were cleaned up
     */
    public int cleanupInvalidHolograms(Map<String, Hologram> holograms) {
        int cleanedUp = 0;
        Iterator<Map.Entry<String, Hologram>> iterator = holograms.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, Hologram> entry = iterator.next();
            String hologramId = entry.getKey();
            Hologram hologram = entry.getValue();

            if (!hologram.isValid()) {
                // Remove from hologram registry
                iterator.remove();

                // Clean up tracking data
                lastUpdateTimes.remove(hologramId);
                pendingUpdates.remove(hologramId);

                cleanedUp++;
                logger.fine("Cleaned up invalid hologram: " + hologramId);
            }
        }

        if (cleanedUp > 0) {
            logger.info("HologramOptimizer cleaned up " + cleanedUp + " invalid holograms");
        }

        return cleanedUp;
    }

    /**
     * Marks a hologram for priority updating on the next optimization cycle.
     *
     * @param hologramId The ID of the hologram to prioritize
     */
    public void markForUpdate(String hologramId) {
        pendingUpdates.add(hologramId);
    }

    /**
     * Gets the current optimization statistics for monitoring purposes.
     *
     * @return Map containing optimization statistics
     */
    public Map<String, Object> getOptimizationStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("trackedHolograms", lastUpdateTimes.size());
        stats.put("pendingUpdates", pendingUpdates.size());
        stats.put("maxUpdateDistance", MAX_UPDATE_DISTANCE);
        stats.put("batchSize", BATCH_SIZE);
        stats.put("minUpdateInterval", MIN_UPDATE_INTERVAL);
        stats.put("onlinePlayerCount", Bukkit.getOnlinePlayers().size());
        return stats;
    }

    /**
     * Resets all optimization tracking data. Useful for debugging or when restarting optimization.
     */
    public void reset() {
        lastUpdateTimes.clear();
        pendingUpdates.clear();
        logger.info("HologramOptimizer reset completed");
    }

    /**
     * Gets the maximum update distance used for proximity filtering.
     *
     * @return Maximum update distance in blocks
     */
    public static int getMaxUpdateDistance() {
        return MAX_UPDATE_DISTANCE;
    }

    /**
     * Gets the batch size used for update processing.
     *
     * @return Batch size for hologram updates
     */
    public static int getBatchSize() {
        return BATCH_SIZE;
    }
}