package me.miguel19877.dev.listeners;

import me.miguel19877.dev.<PERSON>up;
import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.entity.*;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntitySpawnEvent;
import org.bukkit.event.player.PlayerArmorStandManipulateEvent;
import org.bukkit.event.player.PlayerInteractEntityEvent;

public class ShopListener implements Listener {

    @EventHandler
    public void FrameEntityEvent(EntityDamageByEntityEvent e) {
        if (e.getEntity() instanceof ItemFrame || e.getEntity() instanceof ArmorStand) {

                if (e.getDamager() instanceof Player) {
                    Player p = (Player) e.getDamager();
                    if (p.getWorld().getName().equalsIgnoreCase("plotworld")) {
                        return;
                    }
                    if (Permissions.getGrupoId(p) < 12) {
                        e.setCancelled(true);
                    }
                }

        }
        if (e.getDamager() instanceof Projectile) {
            if (((Projectile) e.getDamager()).getShooter() instanceof Player) {
                if (Permissions.getGrupoId((Player) ((Projectile) e.getDamager()).getShooter()) < 12 && !e.getDamager().getWorld().getName().equalsIgnoreCase("loja")) {
                    e.getDamager().remove();
                    e.setCancelled(true);
                }
            }
        }
        if (e.getEntity() instanceof Player) {
            Player damagedPlayer = (Player) e.getEntity();

            Entity damager = e.getDamager();

            // Check if the damager is a player
            if (damager instanceof Player) {
                // Damager is a player
                Rankup.penaltyPlayers.put(damagedPlayer.getUniqueId(), System.currentTimeMillis() + Rankup.PENALTY_DURATION);
                Rankup.penaltyPlayers.put(damager.getUniqueId(), System.currentTimeMillis() + Rankup.PENALTY_DURATION);
            }
            // Check if the damager is a projectile (e.g., arrow) shot by a player
            else if (damager instanceof Projectile) {
                Projectile projectile = (Projectile) damager;
                if (projectile.getShooter() instanceof Player) {
                    // Projectile was shot by a player
                    Rankup.penaltyPlayers.put(damagedPlayer.getUniqueId(), System.currentTimeMillis() + Rankup.PENALTY_DURATION);
                    Rankup.penaltyPlayers.put(((Player) projectile.getShooter()).getUniqueId(), System.currentTimeMillis() + Rankup.PENALTY_DURATION);
                }
            }
        }
    }

    @EventHandler
    public void FrameRotate(PlayerInteractEntityEvent e) {
        if (e.getRightClicked().getType().equals(EntityType.ITEM_FRAME)) {
            if (!e.getRightClicked().getWorld().getName().equalsIgnoreCase("plotworld")) {
                if (Permissions.getGrupoId(e.getPlayer()) < 12) {
                    e.setCancelled(true);
                }
            }
        }
    }

    @EventHandler
    public void armorStandProtect(PlayerArmorStandManipulateEvent e) {
        if (!e.getRightClicked().getWorld().getName().equalsIgnoreCase("plotworld")) {
            if (Permissions.getGrupoId(e.getPlayer()) < 12) {
                e.setCancelled(true);
            }
        }
    }

    @EventHandler
    public void armorStandSpawn(EntitySpawnEvent e) {
        if (e.getEntity() instanceof ArmorStand || e.getEntity() instanceof ItemFrame) {
            //if world name is not plotworld or hub, cancel the event
            if (!e.getLocation().getWorld().getName().equalsIgnoreCase("plotworld") && !e.getLocation().getWorld().getName().equalsIgnoreCase("hub")) {
                // Allow armor stands in "minas" world for holograms and "rankupspawn" world for holograms
                if ((e.getLocation().getWorld().getName().equalsIgnoreCase("minas") && e.getEntity() instanceof ArmorStand) || (e.getLocation().getWorld().getName().equalsIgnoreCase("rankupspawn") && e.getEntity() instanceof ArmorStand)) {
                    // Don't cancel armor stand spawns in minas world - they might be for holograms
                    return;
                }
                e.setCancelled(true);
            }
        }
    }

}
