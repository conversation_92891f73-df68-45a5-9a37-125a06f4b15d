package me.miguel19877.dev.utils;

import me.miguel19877.dev.utils.hologram.Hologram;
import me.miguel19877.dev.utils.hologram.HologramManager;
import me.miguel19877.dev.utils.hologram.HologramBuilder;
import org.bukkit.Bukkit;
import org.bukkit.Chunk;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.world.ChunkLoadEvent;
import org.bukkit.plugin.Plugin;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.lang.reflect.Field;

public class ExampleHolograms implements Listener {

    private final Plugin plugin;
    private final HologramManager hologramManager;
    private final Map<String, String> hologramIds = new HashMap<>(); // Maps custom names to hologram IDs
    private final List<PendingHologram> pendingHolograms = new ArrayList<>(); // Holograms waiting for chunk load
    private final Map<String, HologramData> hologramData = new HashMap<>(); // Stores hologram data for recreation
    private int hologramCounter = 0; // Counter for generating unique IDs

    /**
     * @param plugin The plugin which uses the lib
     */
    public ExampleHolograms(Plugin plugin) {
        this.plugin = plugin;
        this.hologramManager = HologramManager.getInstance();

        // Initialize the hologram manager if not already done
        if (hologramManager != null) {
            hologramManager.initialize(plugin);
        }

        // Register this class as an event listener for chunk load events
        Bukkit.getPluginManager().registerEvents(this, plugin);

        plugin.getLogger().info("ExampleHolograms initialized with chunk loading support");
    }

    /**
     * Inner class to store pending hologram data
     */
    private static class PendingHologram {
        final String customName;
        final String hologramId;
        final Location location;
        final String text;

        PendingHologram(String customName, String hologramId, Location location, String text) {
            this.customName = customName;
            this.hologramId = hologramId;
            this.location = location;
            this.text = text;
        }
    }

    /**
     * Inner class to store hologram data for recreation purposes
     */
    private static class HologramData {
        final Location location;
        final String text;
        final long creationTime;

        HologramData(Location location, String text) {
            this.location = location.clone();
            this.text = text;
            this.creationTime = System.currentTimeMillis();
        }
    }


    /**
     * Deactivates all holograms managed by this instance.
     */
    public void deactivateHolograms() {
        for (String hologramId : hologramIds.values()) {
            hologramManager.removeHologram(hologramId);
        }
        hologramIds.clear();
        hologramData.clear();
    }

    /**
     * Deactivates a specific hologram by its custom name.
     * @param customName The custom name used when creating the hologram
     */
    public void deactivateHologram(String customName) {
        String hologramId = hologramIds.get(customName);
        if (hologramId != null) {
            hologramManager.removeHologram(hologramId);
            hologramIds.remove(customName);
            hologramData.remove(hologramId);
        }
    }

    /**
     * Deactivates a specific hologram by its Hologram object.
     * @param hologram The hologram to deactivate
     */
    public void deactivateHologram(Hologram hologram) {
        if (hologram != null) {
            String hologramId = hologram.getId();
            hologramManager.removeHologram(hologramId);
            // Remove from our tracking maps
            hologramIds.entrySet().removeIf(entry -> entry.getValue().equals(hologramId));
            hologramData.remove(hologramId);
        }
    }

    /**
     * Creates a hologram at the specified location with the given text.
     * If the chunk is not loaded, the hologram will be queued for creation when the chunk loads.
     * @param location The location where the hologram should appear
     * @param text The text to display in the hologram
     */
    public void appendHOLO(Location location, String text) {
        String customName = "holo_" + hologramCounter++;
        String hologramId = "example_" + customName;

        // Check if hologram already exists
        if (hologramManager.hasHologram(hologramId) && hologramManager.isHologramValid(hologramId)) {
            plugin.getLogger().info("Hologram '" + customName + "' already exists, skipping creation");
            return;
        }

        // Check if chunk is loaded
        if (location.getWorld() != null && location.getChunk().isLoaded()) {
            // Chunk is loaded, try to create immediately
            try {
                Hologram hologram = createHologramDirect(hologramId, location, text, 2.0, true);
                if (hologram != null) {
                    if (addHologramToManager(hologramId, hologram)) {
                        hologramIds.put(customName, hologramId);
                        hologramData.put(hologramId, new HologramData(location, text));
                        plugin.getLogger().info("Created hologram '" + customName + "' at " + formatLocation(location));
                    } else {
                        plugin.getLogger().warning("Failed to register hologram '" + customName + "' with manager");
                    }
                } else {
                    // Failed to create, queue for later
                    pendingHolograms.add(new PendingHologram(customName, hologramId, location.clone(), text));
                    plugin.getLogger().warning("Failed to create hologram '" + customName + "', queued for chunk load");
                }
            } catch (Exception e) {
                // Exception occurred, queue for later
                pendingHolograms.add(new PendingHologram(customName, hologramId, location.clone(), text));
                plugin.getLogger().warning("Queued hologram '" + customName + "' due to error: " + e.getMessage());
            }
        } else {
            // Chunk not loaded, queue for when it loads
            pendingHolograms.add(new PendingHologram(customName, hologramId, location.clone(), text));
            plugin.getLogger().info("Queued hologram '" + customName + "' for chunk load at " + formatLocation(location));
        }
    }

    /**
     * Creates a hologram at the specified location with the given text and returns the hologram object.
     * This method only creates holograms if the chunk is already loaded.
     * @param location The location where the hologram should appear
     * @param text The text to display in the hologram
     * @return The created hologram, or null if creation failed or chunk not loaded
     */
    public Hologram appendHOLO2(Location location, String text) {
        String customName = "holo_" + hologramCounter++;
        String hologramId = "example_" + customName;

        // Check if hologram already exists
        if (hologramManager.hasHologram(hologramId) && hologramManager.isHologramValid(hologramId)) {
            plugin.getLogger().info("Hologram '" + customName + "' already exists, returning existing");
            return hologramManager.getHologram(hologramId);
        }

        // Only create if chunk is loaded (don't force load for this method)
        if (location.getWorld() == null || !location.getChunk().isLoaded()) {
            plugin.getLogger().warning("Cannot create hologram '" + customName + "' - chunk not loaded at " + formatLocation(location));
            return null;
        }

        // Try to create the hologram directly
        try {
            Hologram hologram = createHologramDirect(hologramId, location, text, 2.0, true);
            if (hologram != null) {
                if (addHologramToManager(hologramId, hologram)) {
                    hologramIds.put(customName, hologramId);
                    hologramData.put(hologramId, new HologramData(location, text));
                    plugin.getLogger().info("Created hologram '" + customName + "' at " + formatLocation(location));
                    return hologram;
                } else {
                    plugin.getLogger().warning("Failed to register hologram '" + customName + "' with manager");
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to create hologram '" + customName + "': " + e.getMessage());
        }
        return null;
    }

    /**
     * Creates a hologram directly using the Hologram constructor, bypassing HologramManager validation.
     * @param id The hologram ID
     * @param location The location
     * @param text The text
     * @param heightOffset The height offset
     * @param visible Whether visible
     * @return The created hologram
     */
    private Hologram createHologramDirect(String id, Location location, String text, double heightOffset, boolean visible) {
        try {
            return new Hologram(id, location, text, heightOffset, visible);
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to create hologram directly: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Adds a hologram to the manager registry using reflection to bypass validation.
     * @param id The hologram ID
     * @param hologram The hologram instance
     * @return true if successful
     */
    private boolean addHologramToManager(String id, Hologram hologram) {
        try {
            // Use reflection to access the private holograms map
            java.lang.reflect.Field hologramsField = hologramManager.getClass().getDeclaredField("holograms");
            hologramsField.setAccessible(true);
            @SuppressWarnings("unchecked")
            java.util.Map<String, Hologram> holograms = (java.util.Map<String, Hologram>) hologramsField.get(hologramManager);
            holograms.put(id, hologram);
            return true;
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to add hologram to manager registry: " + e.getMessage());
            return false;
        }
    }
    /**
     * Gets the hologram manager instance for advanced operations.
     * @return The HologramManager instance
     */
    public HologramManager getHologramManager() {
        return hologramManager;
    }

    /**
     * Gets a hologram by its custom name.
     * @param customName The custom name used when creating the hologram
     * @return The hologram, or null if not found
     */
    public Hologram getHologram(String customName) {
        String hologramId = hologramIds.get(customName);
        if (hologramId != null) {
            return hologramManager.getHologram(hologramId);
        }
        return null;
    }

    /**
     * Updates the text of a hologram by its custom name.
     * @param customName The custom name of the hologram
     * @param newText The new text to display
     * @return true if the text was updated successfully, false otherwise
     */
    public boolean updateHologramText(String customName, String newText) {
        String hologramId = hologramIds.get(customName);
        if (hologramId != null) {
            return hologramManager.updateHologramText(hologramId, newText);
        }
        return false;
    }

    /**
     * Event handler for chunk load events.
     * Checks if holograms should be spawned in the loaded chunk with a 40-tick delay.
     */
    @EventHandler
    public void onChunkLoad(ChunkLoadEvent event) {
        Chunk loadedChunk = event.getChunk();
        String worldName = loadedChunk.getWorld().getName();
        int chunkX = loadedChunk.getX();
        int chunkZ = loadedChunk.getZ();

        plugin.getLogger().info("Chunk loaded at " + worldName + " [" + chunkX + ", " + chunkZ + "], checking for holograms...");

        // Check pending holograms first
        if (!pendingHolograms.isEmpty()) {
            List<PendingHologram> toProcess = new ArrayList<>();
            for (PendingHologram pending : pendingHolograms) {
                if (pending.location.getWorld() != null &&
                    pending.location.getWorld().getName().equals(worldName) &&
                    (pending.location.getBlockX() >> 4) == chunkX &&
                    (pending.location.getBlockZ() >> 4) == chunkZ) {

                    // Check if hologram is already spawned
                    if (!hologramManager.hasHologram(pending.hologramId) ||
                        !hologramManager.isHologramValid(pending.hologramId)) {
                        toProcess.add(pending);
                    } else {
                        plugin.getLogger().info("Hologram '" + pending.customName + "' already exists, removing from pending");
                        pendingHolograms.remove(pending);
                    }
                }
            }

            // Schedule hologram creation with 40-tick delay
            for (PendingHologram pending : toProcess) {
                scheduleHologramCreation(pending, 40L);
                pendingHolograms.remove(pending);
            }
        }

        // Also check if any existing hologram IDs should be spawned in this chunk
        // This handles cases where holograms were registered but not yet created or were lost
        for (Map.Entry<String, String> entry : hologramIds.entrySet()) {
            String customName = entry.getKey();
            String hologramId = entry.getValue();

            // Check if hologram is already spawned and valid
            if (hologramManager.hasHologram(hologramId) && hologramManager.isHologramValid(hologramId)) {
                continue; // Hologram already exists and is valid
            }

            // Check if we have stored data for this hologram
            HologramData data = hologramData.get(hologramId);
            if (data != null && isLocationInChunk(data.location, worldName, chunkX, chunkZ)) {
                plugin.getLogger().info("Scheduling recreation of hologram '" + customName + "' in loaded chunk");
                attemptHologramRecreation(customName, hologramId, data.location, data.text);
            }
        }
    }

    /**
     * Schedules hologram creation with the specified delay.
     * @param pending The pending hologram to create
     * @param delayTicks The delay in ticks before creation
     */
    private void scheduleHologramCreation(PendingHologram pending, long delayTicks) {
        plugin.getLogger().info("Scheduling hologram '" + pending.customName + "' creation with " + delayTicks + " tick delay");

        Bukkit.getScheduler().runTaskLater(plugin, new Runnable() {
            @Override
            public void run() {
                // Double-check that hologram doesn't already exist before creating
                if (hologramManager.hasHologram(pending.hologramId) &&
                    hologramManager.isHologramValid(pending.hologramId)) {
                    plugin.getLogger().info("Hologram '" + pending.customName + "' already exists, skipping creation");
                    return;
                }

                // Check if chunk is still loaded
                if (pending.location.getWorld() == null || !pending.location.getChunk().isLoaded()) {
                    plugin.getLogger().warning("Chunk unloaded before hologram '" + pending.customName + "' could be created, re-queuing");
                    pendingHolograms.add(pending);
                    return;
                }

                try {
                    Hologram hologram = createHologramDirect(
                        pending.hologramId,
                        pending.location,
                        pending.text,
                        2.0,
                        true
                    );
                    if (hologram != null && addHologramToManager(pending.hologramId, hologram)) {
                        hologramIds.put(pending.customName, pending.hologramId);
                        hologramData.put(pending.hologramId, new HologramData(pending.location, pending.text));
                        plugin.getLogger().info("Successfully created delayed hologram '" + pending.customName + "' at " + formatLocation(pending.location));
                    } else {
                        plugin.getLogger().warning("Failed to create or register delayed hologram '" + pending.customName + "'");
                        // Re-queue for another attempt
                        pendingHolograms.add(pending);
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("Failed to create delayed hologram '" + pending.customName + "': " + e.getMessage());
                    // Re-queue for another attempt
                    pendingHolograms.add(pending);
                }
            }
        }, delayTicks);
    }

    /**
     * Formats a location for logging purposes.
     */
    private String formatLocation(Location location) {
        if (location.getWorld() == null) {
            return "unknown world";
        }
        return String.format("%s: %.1f, %.1f, %.1f",
            location.getWorld().getName(),
            location.getX(),
            location.getY(),
            location.getZ());
    }

    /**
     * Checks if a location is within the specified chunk coordinates.
     * @param location The location to check
     * @param worldName The world name
     * @param chunkX The chunk X coordinate
     * @param chunkZ The chunk Z coordinate
     * @return true if the location is in the specified chunk
     */
    private boolean isLocationInChunk(Location location, String worldName, int chunkX, int chunkZ) {
        if (location.getWorld() == null || !location.getWorld().getName().equals(worldName)) {
            return false;
        }

        int locationChunkX = location.getBlockX() >> 4;
        int locationChunkZ = location.getBlockZ() >> 4;

        return locationChunkX == chunkX && locationChunkZ == chunkZ;
    }

    /**
     * Attempts to recreate a hologram that was registered but may have been lost.
     * This is used when chunks load and we want to ensure all holograms are present.
     * @param customName The custom name of the hologram
     * @param hologramId The hologram ID
     * @param location The location where the hologram should be
     * @param text The text for the hologram
     */
    private void attemptHologramRecreation(String customName, String hologramId, Location location, String text) {
        // Check if hologram already exists and is valid
        if (hologramManager.hasHologram(hologramId) && hologramManager.isHologramValid(hologramId)) {
            return; // Already exists and valid
        }

        // Check if chunk is loaded
        if (location.getWorld() == null || !location.getChunk().isLoaded()) {
            plugin.getLogger().info("Cannot recreate hologram '" + customName + "' - chunk not loaded");
            return;
        }

        // Schedule recreation with delay
        PendingHologram pending = new PendingHologram(customName, hologramId, location.clone(), text);
        scheduleHologramCreation(pending, 40L);
        plugin.getLogger().info("Scheduled recreation of hologram '" + customName + "' with 40-tick delay");
    }

    /**
     * Gets the number of pending holograms waiting for chunk loads.
     * @return The number of pending holograms
     */
    public int getPendingHologramCount() {
        return pendingHolograms.size();
    }

    /**
     * Retries creating pending holograms without forcing chunk loads.
     * This is called periodically to attempt creation when chunks become available.
     */
    public void retryPendingHolograms() {
        if (pendingHolograms.isEmpty()) {
            return;
        }

        List<PendingHologram> toProcess = new ArrayList<>(pendingHolograms);
        for (PendingHologram pending : toProcess) {
            if (pending.location.getWorld() != null && pending.location.getChunk().isLoaded()) {
                try {
                    Hologram hologram = createHologramDirect(
                        pending.hologramId,
                        pending.location,
                        pending.text,
                        2.0,
                        true
                    );
                    if (hologram != null && addHologramToManager(pending.hologramId, hologram)) {
                        hologramIds.put(pending.customName, pending.hologramId);
                        plugin.getLogger().info("Created pending hologram '" + pending.customName + "' at " + formatLocation(pending.location));
                        pendingHolograms.remove(pending);
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("Failed to create pending hologram '" + pending.customName + "': " + e.getMessage());
                }
            }
        }

        if (!pendingHolograms.isEmpty()) {
            plugin.getLogger().info(pendingHolograms.size() + " holograms still pending chunk load");
        }
    }

    /**
     * Forces creation of all pending holograms by loading their chunks.
     * Use with caution as this can cause performance issues.
     */
    public void forceCreatePendingHolograms() {
        if (pendingHolograms.isEmpty()) {
            return;
        }

        plugin.getLogger().info("Force-loading " + pendingHolograms.size() + " pending holograms...");

        List<PendingHologram> toProcess = new ArrayList<>(pendingHolograms);
        for (PendingHologram pending : toProcess) {
            if (pending.location.getWorld() != null) {
                // Force load the chunk
                pending.location.getChunk().load(true);

                try {
                    Hologram hologram = createHologramDirect(
                        pending.hologramId,
                        pending.location,
                        pending.text,
                        2.0,
                        true
                    );
                    if (hologram != null && addHologramToManager(pending.hologramId, hologram)) {
                        hologramIds.put(pending.customName, pending.hologramId);
                        plugin.getLogger().info("Force-created hologram '" + pending.customName + "' at " + formatLocation(pending.location));
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("Failed to force-create hologram '" + pending.customName + "': " + e.getMessage());
                }
                pendingHolograms.remove(pending);
            }
        }
    }

    /**
     * Test method to create a hologram at a specific location for debugging.
     * @param location The location to test
     * @param text The text to display
     * @return true if successful
     */
    public boolean testHologramCreation(Location location, String text) {
        plugin.getLogger().info("Testing hologram creation at " + formatLocation(location));

        // Check world and chunk status
        if (location.getWorld() == null) {
            plugin.getLogger().warning("World is null!");
            return false;
        }

        plugin.getLogger().info("World: " + location.getWorld().getName());
        plugin.getLogger().info("Chunk loaded: " + location.getChunk().isLoaded());

        // Force load chunk if needed
        if (!location.getChunk().isLoaded()) {
            plugin.getLogger().info("Loading chunk...");
            location.getChunk().load(true);
            plugin.getLogger().info("Chunk loaded after force: " + location.getChunk().isLoaded());
        }

        // Try direct creation
        try {
            String testId = "test_hologram_" + System.currentTimeMillis();
            Hologram hologram = createHologramDirect(testId, location, text, 2.0, true);
            if (hologram != null) {
                if (addHologramToManager(testId, hologram)) {
                    plugin.getLogger().info("Successfully created test hologram!");
                    return true;
                } else {
                    plugin.getLogger().warning("Failed to register test hologram with manager");
                }
            } else {
                plugin.getLogger().warning("Failed to create test hologram - hologram is null");
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Exception creating test hologram: " + e.getMessage());
            e.printStackTrace();
        }

        return false;
    }

    /**
     * Cleanup method to be called when the plugin is disabled.
     * Removes all holograms and shuts down the hologram manager.
     */
    public void cleanup() {
        deactivateHolograms();
        pendingHolograms.clear();
        hologramData.clear();
        hologramManager.cleanup();
    }

    // Note: The old hologram library events (PlayerHologramShowEvent, PlayerHologramHideEvent, PlayerHologramInteractEvent)
    // are not available in the new hologram system. If you need similar functionality, you would need to implement
    // custom event handling using the new hologram system's capabilities or create custom events.
}